<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Sunday Calendar Examples</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
    
    <style>
        .example-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        .code-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="container mt-5">
        <h1>Sunday Calendar Component Examples</h1>
        <p class="lead">This page demonstrates different ways to use the reusable Sunday Calendar component.</p>

        <!-- Example 1: Basic Calendar -->
        <div class="example-section">
            <h3>Example 1: Basic Calendar</h3>
            <p>Simple calendar with default settings and auto-selection of most recent Sunday.</p>
            
            <div class="code-example">
                {% verbatim %}{% include 'includes/sunday_calendar.html' with calendar_id='basicCalendar' %}{% endverbatim %}
            </div>
            
            {% include 'includes/sunday_calendar.html' with calendar_id='basicCalendar' %}
        </div>

        <!-- Example 2: Calendar with Hidden Input -->
        <div class="example-section">
            <h3>Example 2: Calendar with Form Integration</h3>
            <p>Calendar that updates a hidden form field when a date is selected.</p>
            
            <div class="code-example">
                {% verbatim %}{% include 'includes/sunday_calendar.html' with calendar_id='formCalendar' hidden_input_id='selectedWeekEnd' %}{% endverbatim %}
            </div>
            
            <form>
                {% include 'includes/sunday_calendar.html' with calendar_id='formCalendar' hidden_input_id='selectedWeekEnd' %}
                
                <div class="mt-3">
                    <label for="selectedWeekEnd">Selected Week Ending Date:</label>
                    <input type="text" id="selectedWeekEnd" name="selectedWeekEnd" class="form-control" readonly>
                </div>
                
                <button type="button" class="btn btn-primary mt-3" onclick="showSelectedDate()">Show Selected Date</button>
            </form>
        </div>

        <!-- Example 3: Calendar without Instructions -->
        <div class="example-section">
            <h3>Example 3: Compact Calendar (No Instructions)</h3>
            <p>Calendar with instructions hidden for a more compact display.</p>
            
            <div class="code-example">
                {% verbatim %}{% include 'includes/sunday_calendar.html' with calendar_id='compactCalendar' show_instructions=false %}{% endverbatim %}
            </div>
            
            {% include 'includes/sunday_calendar.html' with calendar_id='compactCalendar' show_instructions=false %}
        </div>

        <!-- Example 4: Calendar without Auto-Selection -->
        <div class="example-section">
            <h3>Example 4: Calendar without Auto-Selection</h3>
            <p>Calendar that doesn't automatically select the most recent Sunday.</p>
            
            <div class="code-example">
                {% verbatim %}{% include 'includes/sunday_calendar.html' with calendar_id='manualCalendar' auto_select_recent_sunday=false %}{% endverbatim %}
            </div>
            
            {% include 'includes/sunday_calendar.html' with calendar_id='manualCalendar' auto_select_recent_sunday=false %}
        </div>

        <!-- Usage Instructions -->
        <div class="example-section">
            <h3>Usage Instructions</h3>
            <h5>Basic Usage:</h5>
            <div class="code-example">
                {% verbatim %}{% include 'includes/sunday_calendar.html' with calendar_id='myCalendar' %}{% endverbatim %}
            </div>

            <h5>Available Parameters:</h5>
            <ul>
                <li><strong>calendar_id</strong> (required): Unique ID for the calendar container</li>
                <li><strong>hidden_input_id</strong> (optional): ID of hidden input field to update with selected date</li>
                <li><strong>show_instructions</strong> (optional): Whether to show instructions (default: true)</li>
                <li><strong>auto_select_recent_sunday</strong> (optional): Whether to auto-select the most recent Sunday (default: true)</li>
            </ul>

            <h5>JavaScript Integration:</h5>
            <div class="code-example">
// Access the calendar instance
var calendar = window.sundayCalendarInstance;

// Get selected Sunday
var selectedDate = calendar.getSelectedSunday();

// Set selected Sunday programmatically
calendar.setSelectedSunday(new Date());

// Custom callback for date selection
window.onSundayCalendarSelect = function(selectedSunday) {
    console.log('Selected Sunday:', selectedSunday);
};
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>

    <script>
        function showSelectedDate() {
            const selectedDate = document.getElementById('selectedWeekEnd').value;
            if (selectedDate) {
                alert('Selected week ending date: ' + selectedDate);
            } else {
                alert('No date selected yet. Please select a date from the calendar.');
            }
        }
    </script>
</body>
</html>
