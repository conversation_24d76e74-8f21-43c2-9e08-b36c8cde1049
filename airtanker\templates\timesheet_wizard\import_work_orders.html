<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css"
          integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" rel="stylesheet">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/1.7.1/css/buttons.dataTables.min.css">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/styles_odoo.css') }}"  />

    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            position: relative;
            cursor: pointer;
        }
        /* Add this style for 'error' messages */
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .flash-message {
            position: absolute;
            z-index: 1000; /* Ensures it's above other content */
            display: none; /* Initially hidden */
            opacity: 0; /* Start fully transparent */
            transition: opacity 2s; /* Smooth transition for fading */
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        .progress-container {
            width: 80%;
            background-color: #e0e0e0;
            position: fixed; /* Stick to the top */
            z-index: 1000;
            top: 50px;
            left: 0;
            right: 0; /* Added to work with margin: auto; */
            margin: auto; /* Center the element */
        }

        
        .progress-bar {
            height: 7px;
            background-color: #4CAF50;
            width: 33%; /* Start state */
            animation-name: loadProgress;
            animation-duration: 2s; /* Customize this value */
            animation-fill-mode: forwards; /* Keeps the state at 66% after animation */
        }
        @keyframes loadProgress {
            from {
                width: 0%;
            }
            to {
                width: 10%;
            }
        }

        .progress {
            position: relative;
            width: 100%;
            height: 30px;
            background-color: #f3f3f3;
        }

        .indeterminate {
            position: absolute;
            height: 100%;
            background-color: #29d;
            animation: indeterminate 2s linear infinite;
        }

        @keyframes indeterminate {
            0% {
                left: -40%;
                width: 40%;
            }
            50% {
                width: 30%;
            }
            100% {
                left: 100%;
            }
        }
        div#loading {
            width: 500px;
            height: 500px;
            display: none;
            background: url(/static/assets/loadingOdoo.gif) no-repeat center center;
            background-size: contain;
            cursor: wait;
            z-index: 1000;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            
            box-shadow: none; /* Ensure no shadow is applied */
            filter: none; /* Remove any filters that might create a shadow effect */
        }


    </style>
    <title>Import Work Orders</title>
</head>


<br>


<body class="bg-gradient-white">
    <!-- Confirmation Modal -->
    <div class="modal fade" id="confirmationModal" tabindex="-1" role="dialog" aria-labelledby="confirmationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmationModalLabel">Confirm Skip</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Please confirm that the correct week ending date is selected if you wish to skip this step.</p>
                    <div class="form-group">
                        <label for="weekEndingDate">Week Ending Date</label>
                        <input type="date" class="form-control" id="weekEndingDate" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="confirmSkip()">Confirm</button>
                </div>
            </div>
        </div>
    </div>


    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    <div id="progressLabel" class="fadeIn" style="display: block; z-index: 1000;text-align: center; position: fixed; width: 100%; top: 20px;">
        Step 1: Import Work Orders
    </div>
    <div id="loading"></div>
    <div class="wrapper fadeInDown" id="content">

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-message">
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}" style="margin-top: -230px;">{{ message }}</div>
                {% endfor %}
                </div>
            {% endif %}
        {% endwith %}
            <div id="formContent">
                <h1 class="h4 text-gray-900 mb-4 fadeIn first" style="margin-top: 5%;">                                            
                    Upload Timesheet Reports
                </h1>
                <p>Last Import: <strong>{{ last_import }}</strong></p>
                <form method="POST" action="{{ url_for('import_work_orders') }}">
                    <div>
                        <label for="sundayPicker">{{ form.sundayPicker.label.text }}</label><br>

                        <!-- Include the Sunday Calendar Component -->
                        {% include 'includes/sunday_calendar.html' with calendar_id='workOrderCalendar' hidden_input_id='sundayPicker' %}

                        {% if form.sundayPicker.errors %}
                            <ul class="errors">
                                {% for error in form.sundayPicker.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            </ul>
                        {% endif %}
                    </div>

                    <input type="submit" class="underlineHover fadeIn second" onclick="loading();" value="Import Work Orders">
                </form>
                <div id="formFooter">
                    <a class="underlineHover fadeIn third" href="#" onclick="showConfirmationModal()">Skip This Step</a>
                </div>
                <div id="formFooter">
                    <a class="underlineHover fadeIn third" href="/">Cancel</a>
                </div>
            </div>
        </div>
    </div>

  </body>
</html>


<script src="https://code.jquery.com/jquery-3.2.1.slim.min.js"
        integrity="sha384-KJ3o2DKtIkvYIK3UENzmM7KCkRr/rE9/Qpg6aAZGJwFDMVNA/GpGFF93hXpG5KkN"
        crossorigin="anonymous"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"
        integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q"
        crossorigin="anonymous"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"
        integrity="sha384-JZR6Spejh4U02d8jOt6vLEHfe/JQGiRRSQQxSfFWpi1MquVdAyjUar5+76PVCmYl"
        crossorigin="anonymous"></script>

    <script type="text/javascript">// <![CDATA[
        function loading(){
            $("#loading").show();
            $("#content").hide();       
        }
// ]]></script>
<script>

    // Sunday calendar component handles auto-selection of most recent Sunday

    function showConfirmationModal() {
        // Get selected date from the Sunday calendar
        const selectedDate = document.getElementById('sundayPicker').value;
        document.getElementById('weekEndingDate').value = selectedDate;
        $('#confirmationModal').modal('show');
    }

    function confirmSkip() {
        const weekEndingDate = document.getElementById('weekEndingDate').value;
        if (!weekEndingDate) {
            alert("Please select a week ending date before confirming.");
            return;
        }

        // Logic to handle the skip action with the selected date
        // For example, redirecting with the date as a query parameter
        const skipUrl = `/upload_internal_timesheets?week_ending_date=${encodeURIComponent(weekEndingDate)}`;
        window.location.href = skipUrl;
    }

    // Custom callback for when a Sunday is selected in the calendar
    window.onSundayCalendarSelect = function(selectedSunday) {
        // Update the modal's week ending date field
        const year = selectedSunday.getFullYear();
        const month = String(selectedSunday.getMonth() + 1).padStart(2, '0');
        const day = String(selectedSunday.getDate()).padStart(2, '0');
        const formattedDate = `${year}-${month}-${day}`;

        // Update the confirmation modal's date field
        const weekEndingDateField = document.getElementById('weekEndingDate');
        if (weekEndingDateField) {
            weekEndingDateField.value = formattedDate;
        }
    };


    document.addEventListener('DOMContentLoaded', (event) => {
        const flashMessage = document.querySelector('.flash-message');
        if (flashMessage) {
            flashMessage.style.display = 'block'; // Make it visible
            flashMessage.style.opacity = 1; // Fade in
            setTimeout(() => {
                flashMessage.style.opacity = 0; // Fade out after 5 seconds
                setTimeout(() => flashMessage.remove(), 2000); // Remove from DOM after it's invisible
            }, 2500);
        }
    });
    document.addEventListener("DOMContentLoaded", (event) => {
        setTimeout(() => {
            document.getElementById('progressLabel').style.display = 'block';
        }, 2000); // Match this duration to your CSS animation-duration
    });
    sessionStorage.removeItem('errors');

    </script>
                                               
</body>
</html>