{% comment %}
Sunday Calendar Component

Usage:
{% include 'includes/sunday_calendar.html' with calendar_id='myCalendar' hidden_input_id='sundayPicker' %}

Parameters:
- calendar_id: Unique ID for the calendar container (required)
- hidden_input_id: ID of hidden input field to update with selected date (optional)
- show_instructions: Whether to show instructions (default: true)
- auto_select_recent_sunday: Whether to auto-select the most recent Sunday (default: true)

Example with all parameters:
{% include 'includes/sunday_calendar.html' with calendar_id='weekSelector' hidden_input_id='weekEndingDate' show_instructions=false auto_select_recent_sunday=true %}
{% endcomment %}

<!-- Sunday Calendar CSS -->
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/sunday-calendar.css') }}">

<!-- Calendar Container -->
<div id="{{ calendar_id|default:'sundayCalendar' }}" class="sunday-calendar-container"></div>

<!-- Hidden input field to store selected date (if specified) -->
{% if hidden_input_id %}
<input type="hidden" id="{{ hidden_input_id }}" name="{{ hidden_input_id }}" />
{% endif %}

<!-- Sunday Calendar JavaScript -->
<script src="{{ url_for('static', filename='js/sunday-calendar.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the Sunday Calendar
    const calendarId = '{{ calendar_id|default:"sundayCalendar" }}';
    const hiddenInputId = '{{ hidden_input_id|default:"" }}';
    const autoSelectRecentSunday = {{ auto_select_recent_sunday|default:true|yesno:"true,false" }};
    
    // Calendar options
    const options = {
        onDateSelect: function(selectedSunday) {
            // Update hidden input if specified
            if (hiddenInputId) {
                const hiddenInput = document.getElementById(hiddenInputId);
                if (hiddenInput) {
                    // Format date as YYYY-MM-DD for form submission
                    const year = selectedSunday.getFullYear();
                    const month = String(selectedSunday.getMonth() + 1).padStart(2, '0');
                    const day = String(selectedSunday.getDate()).padStart(2, '0');
                    hiddenInput.value = `${year}-${month}-${day}`;
                    
                    // Trigger change event for any listeners
                    hiddenInput.dispatchEvent(new Event('change'));
                }
            }
            
            // Custom callback if defined globally
            if (typeof window.onSundayCalendarSelect === 'function') {
                window.onSundayCalendarSelect(selectedSunday);
            }
        }
    };
    
    // Create calendar instance
    const calendar = new SundayCalendar(calendarId, options);
    
    // Auto-select most recent Sunday if enabled
    if (autoSelectRecentSunday) {
        const today = new Date();
        const dayOfWeek = today.getDay(); // Sunday - 0, Monday - 1, ..., Saturday - 6
        const difference = dayOfWeek % 7; // Calculate difference to get back to the previous Sunday
        const mostRecentSunday = new Date(today);
        mostRecentSunday.setDate(today.getDate() - difference);
        
        calendar.setSelectedSunday(mostRecentSunday);
    }
    
    // Store calendar instance globally for external access
    window.sundayCalendarInstance = calendar;
});
</script>

<style>
/* Override instructions display if needed */
{% if show_instructions == false %}
.sunday-calendar .instructions {
    display: none;
}
{% endif %}

/* Additional styling for integration */
.sunday-calendar-container {
    margin: 20px 0;
}
</style>
