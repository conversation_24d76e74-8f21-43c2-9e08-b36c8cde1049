<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>Sunday Calendar Component Example</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Sunday Calendar CSS -->
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/sunday-calendar.css') }}">
    
    <style>
        .example-section {
            margin: 40px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .example-title {
            color: #333;
            margin-bottom: 20px;
        }
        
        .code-example {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <h1 class="text-center mb-4">Sunday Calendar Component Examples</h1>
        
        <!-- Example 1: Basic Calendar -->
        <div class="example-section">
            <h3 class="example-title">1. Basic Sunday Calendar</h3>
            <p>A standalone calendar component for selecting Sundays:</p>
            <div id="basic-calendar"></div>
            <div class="code-example">
                <strong>JavaScript:</strong><br>
                const calendar = new SundayCalendar('basic-calendar', {<br>
                &nbsp;&nbsp;onDateSelect: (sunday) => console.log('Selected:', sunday)<br>
                });
            </div>
        </div>
        
        <!-- Example 2: Form Integration -->
        <div class="example-section">
            <h3 class="example-title">2. Form Integration</h3>
            <p>Replace a date input with the calendar component:</p>
            
            <form>
                <div class="form-group">
                    <label for="week-ending-input">Week Ending Date:</label>
                    <input type="date" id="week-ending-input" class="form-control" style="max-width: 200px;">
                </div>
                <div id="form-calendar-container"></div>
                <button type="button" class="btn btn-primary" onclick="showSelectedDate()">Show Selected Date</button>
            </form>
            
            <div class="code-example">
                <strong>JavaScript:</strong><br>
                replaceDateInputWithCalendar('week-ending-input', 'form-calendar-container');
            </div>
        </div>
        
        <!-- Example 3: Compact Calendar -->
        <div class="example-section">
            <h3 class="example-title">3. Compact Calendar</h3>
            <p>A smaller version suitable for dashboards:</p>
            <div id="compact-calendar" class="compact"></div>
            <div class="code-example">
                <strong>JavaScript:</strong><br>
                createCompactCalendar('compact-calendar');
            </div>
        </div>
        
        <!-- Example 4: Modal Calendar -->
        <div class="example-section">
            <h3 class="example-title">4. Modal Calendar</h3>
            <p>Calendar inside a modal dialog:</p>
            
            <input type="date" id="modal-input" class="form-control" style="max-width: 200px; display: inline-block;">
            <button type="button" class="btn btn-secondary ml-2" data-toggle="modal" data-target="#calendarModal">
                Open Calendar
            </button>
            
            <!-- Modal -->
            <div class="modal fade" id="calendarModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Select Week Ending Date</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <!-- Calendar will be inserted here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="code-example">
                <strong>JavaScript:</strong><br>
                createModalCalendar('calendarModal', 'modal-input');
            </div>
        </div>
        
        <!-- Example 5: Auto-initialization -->
        <div class="example-section">
            <h3 class="example-title">5. Auto-initialization</h3>
            <p>Automatically replace existing date pickers:</p>
            
            <div class="code-example">
                <strong>HTML:</strong><br>
                &lt;input type="date" id="sundayPicker" class="form-control"&gt;<br>
                &lt;!-- or --&gt;<br>
                &lt;input type="date" id="weekEndingPicker" class="form-control"&gt;<br><br>
                
                <strong>JavaScript:</strong><br>
                document.addEventListener('DOMContentLoaded', autoInitializeCalendars);
            </div>
        </div>
        
        <!-- Integration Instructions -->
        <div class="example-section">
            <h3 class="example-title">Integration Instructions</h3>
            <div class="code-example">
                <strong>1. Include the required files in your template:</strong><br>
                &lt;link rel="stylesheet" href="{{ url_for('static', filename='css/sunday-calendar.css') }}"&gt;<br>
                &lt;script src="{{ url_for('static', filename='js/sunday-calendar.js') }}"&gt;&lt;/script&gt;<br>
                &lt;script src="{{ url_for('static', filename='js/sunday-calendar-integration.js') }}"&gt;&lt;/script&gt;<br><br>
                
                <strong>2. For automatic replacement of existing date pickers:</strong><br>
                document.addEventListener('DOMContentLoaded', autoInitializeCalendars);<br><br>
                
                <strong>3. For manual integration:</strong><br>
                const calendar = new SundayCalendar('container-id', options);<br>
                // or<br>
                replaceDateInputWithCalendar('input-id', 'container-id', options);
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Sunday Calendar JS -->
    <script src="{{ url_for('static', filename='js/sunday-calendar.js') }}"></script>
    <script src="{{ url_for('static', filename='js/sunday-calendar-integration.js') }}"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Example 1: Basic Calendar
            const basicCalendar = new SundayCalendar('basic-calendar', {
                onDateSelect: (sunday) => {
                    console.log('Basic calendar selected:', sunday);
                }
            });
            
            // Example 2: Form Integration
            const formCalendar = replaceDateInputWithCalendar('week-ending-input', 'form-calendar-container', {
                onDateSelect: (sunday) => {
                    console.log('Form calendar selected:', sunday);
                }
            });
            
            // Example 3: Compact Calendar
            const compactCalendar = createCompactCalendar('compact-calendar', {
                onDateSelect: (sunday) => {
                    console.log('Compact calendar selected:', sunday);
                }
            });
            
            // Example 4: Modal Calendar
            const modalCalendar = createModalCalendar('calendarModal', 'modal-input', {
                onDateSelect: (sunday) => {
                    console.log('Modal calendar selected:', sunday);
                }
            });
        });
        
        function showSelectedDate() {
            const input = document.getElementById('week-ending-input');
            if (input.value) {
                alert('Selected date: ' + input.value);
            } else {
                alert('No date selected');
            }
        }
    </script>
</body>
</html>
