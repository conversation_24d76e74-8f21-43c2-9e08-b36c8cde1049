# Sunday Calendar Component

A reusable, interactive calendar component that emphasizes Sunday selection and week highlighting. Perfect for applications that need week-ending date selection.

## Features

- **Sunday-focused**: Highlights Sundays and automatically selects the Sunday of any clicked week
- **Week highlighting**: Hover over any day to highlight the entire week with Sunday emphasis
- **Responsive design**: Works on desktop and mobile devices
- **Easy integration**: Simple Django template include with customizable parameters
- **Form integration**: Automatically updates hidden form fields
- **Keyboard accessible**: Supports keyboard navigation
- **Customizable**: Multiple configuration options for different use cases

## Files

- `static/js/sunday-calendar.js` - Main JavaScript component
- `static/css/sunday-calendar.css` - Styling for the calendar
- `templates/includes/sunday_calendar.html` - Django template include
- `templates/examples/sunday_calendar_example.html` - Usage examples

## Quick Start

### 1. Basic Usage

```html
{% include 'includes/sunday_calendar.html' with calendar_id='myCalendar' %}
```

### 2. With Form Integration

```html
<form method="POST">
    {% include 'includes/sunday_calendar.html' with calendar_id='weekSelector' hidden_input_id='weekEndingDate' %}
    <input type="submit" value="Submit">
</form>
```

### 3. Compact Version

```html
{% include 'includes/sunday_calendar.html' with calendar_id='compactCal' show_instructions=false %}
```

## Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `calendar_id` | string | 'sundayCalendar' | Unique ID for the calendar container |
| `hidden_input_id` | string | None | ID of hidden input field to update with selected date |
| `show_instructions` | boolean | true | Whether to show usage instructions |
| `auto_select_recent_sunday` | boolean | true | Whether to auto-select the most recent Sunday |

## JavaScript API

### Accessing the Calendar Instance

```javascript
// The calendar instance is available globally
var calendar = window.sundayCalendarInstance;
```

### Methods

```javascript
// Get the currently selected Sunday
var selectedDate = calendar.getSelectedSunday();

// Set selected Sunday programmatically
calendar.setSelectedSunday(new Date());

// Navigate to a specific month
calendar.currentDate = new Date(2024, 5, 1); // June 2024
calendar.render();
```

### Events

```javascript
// Custom callback for date selection
window.onSundayCalendarSelect = function(selectedSunday) {
    console.log('Selected Sunday:', selectedSunday);
    // Your custom logic here
};
```

## Styling

The component uses CSS custom properties for easy theming:

```css
.sunday-calendar {
    --primary-color: #3b82f6;
    --hover-color: #dbeafe;
    --selected-color: #3b82f6;
    --sunday-color: #2563eb;
}
```

## Integration Examples

### Replace Existing Date Input

**Before:**
```html
<input type="date" id="sundayPicker" name="sundayPicker" onchange="checkSunday(this)">
```

**After:**
```html
{% include 'includes/sunday_calendar.html' with calendar_id='workOrderCalendar' hidden_input_id='sundayPicker' %}
```

### Multiple Calendars on Same Page

```html
<!-- Start date calendar -->
{% include 'includes/sunday_calendar.html' with calendar_id='startCalendar' hidden_input_id='startDate' %}

<!-- End date calendar -->
{% include 'includes/sunday_calendar.html' with calendar_id='endCalendar' hidden_input_id='endDate' %}
```

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Dependencies

- No external JavaScript dependencies
- Uses modern CSS Grid and Flexbox
- Compatible with Bootstrap 4+ (optional)

## Customization

### Custom Styling

Override CSS classes to match your application's theme:

```css
.sunday-calendar {
    border-radius: 12px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.calendar-day.sunday {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

### Custom Behavior

Extend the component with additional functionality:

```javascript
// Add custom validation
window.onSundayCalendarSelect = function(selectedSunday) {
    const today = new Date();
    if (selectedSunday > today) {
        alert('Cannot select future dates');
        return false;
    }
    
    // Continue with normal processing
    updateFormField(selectedSunday);
};
```

## Troubleshooting

### Calendar Not Displaying
- Ensure CSS file is loaded: Check browser dev tools for 404 errors
- Verify container ID is unique on the page
- Check JavaScript console for errors

### Date Not Updating Form Field
- Verify `hidden_input_id` matches your input field ID
- Check that the input field exists when calendar initializes
- Ensure no JavaScript errors are preventing execution

### Styling Issues
- CSS conflicts: Use more specific selectors or `!important`
- Bootstrap conflicts: Load calendar CSS after Bootstrap
- Mobile display: Check viewport meta tag is present

## Migration from Standard Date Input

1. Replace `<input type="date">` with calendar include
2. Update JavaScript that references the old input
3. Remove custom date validation (calendar handles Sunday selection)
4. Test form submission with new hidden field

## Performance Notes

- Calendar renders efficiently using DocumentFragment
- Event delegation minimizes memory usage
- CSS transitions provide smooth interactions
- Responsive design adapts to screen size automatically
