from main import airtanker_app
from flask import render_template, request, jsonify, session, flash, redirect, url_for

from endpoints.decorators import requires_authentication, requires_odoo_authentication

from services.EmployeeService import push_updates, push_updates_customer, update_travel_hours_for_not_errored_employees
from services.OdooService import OdooService
from services.ExcelFileService import ExcelFileService
from services.PaycorService import PaycorService

from forms.WorkOrderForm import *
from services.DatabaseService import DatabaseService
from datetime import datetime, timedelta


# Step 1 - Import Work Orders From Odoo
@airtanker_app.route('/import_work_orders', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def import_work_orders():
    form = WorkOrderForm()

    if 'internal_errors_found' in session:
        session.pop('internal_errors_found', None)

    if request.method in ('GET'):
        database_service = DatabaseService()
        database_service.connect()
        last_import = "No Imports Yet."

        query = """
            SELECT TOP 1 [ImportDate]
            FROM [dbo].[WorkOrders]
            ORDER BY [ImportDate] DESC
        """
        results = database_service.execute_query(query)

        if results:
            last_import_date = results[0]["ImportDate"]
            adjusted_import_date = last_import_date - timedelta(hours=4)  # Subtract 4 hours            
            today_date = datetime.now().date()
            
            if adjusted_import_date.date() == today_date:
                last_import = f"Today at {adjusted_import_date.strftime('%I:%M %p')}"
            else:
                last_import = adjusted_import_date.strftime("%Y-%m-%d %I:%M %p")

        database_service.disconnect()
        return render_template('timesheet_wizard/import_work_orders.html', form=form, last_import=last_import)
    
    if form.validate_on_submit():  # This will automatically validate the CSRF token
        selected_week_ending = form.sundayPicker.data  # Access the selected date
        session['selected_week_ending'] = selected_week_ending
        odoo_service = session['odoo_service'] # type: OdooService
        odoo_service.import_work_orders_to_db(selected_week_ending)

        # Add step complete to session

        # redirect to step 2 - import internal sheets
        return redirect(url_for('upload_internal_timesheets') + f'?week_ending_date={selected_week_ending}')

# Step 2 - Upload Internal Sheets
@airtanker_app.route('/upload_internal_timesheets', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def upload_internal_timesheets():
    if request.args.get("week_ending_date"):
        session['selected_week_ending'] = request.args.get("week_ending_date")

    if request.method in ('POST'):
        excel_file_service = ExcelFileService()

        excel_file_service.name_errors = []
        excel_file_service.error_logs = []

        uploaded_files = request.files.getlist('uploaded_files[]')        
        errors, name_errors, fileIds = excel_file_service.parse_internal_files(uploaded_files,
                                                                        session['selected_week_ending'],
                                                                        session["odoo_service"])
        if errors or name_errors:
            redirect_url = url_for('resolve_internal_errors')
            return jsonify({'redirect_url': redirect_url, "errors":errors, "name_errors":name_errors, "fileIds":fileIds})
        else:
            redirect_url = url_for('upload_customer_timesheets')
            return jsonify({'redirect_url': redirect_url})

    return render_template('timesheet_wizard/upload_internal_timesheets.html')

@airtanker_app.route('/import_paycor_timesheets', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def import_paycor_timesheets():
    selected_week_ending = False
    if request.args.get("week_ending_date"):
        selected_week_ending = request.args.get("week_ending_date")
        import_type = "external"

    if request.form:
        selected_week_ending = request.form['week_ending_date']
        import_type = request.form['type'] 

    session['selected_week_ending'] = selected_week_ending
    session["import_type"] = import_type

    if request.method in ('POST'):        
        # Get Paycor API Service
        # paycor_service = PaycorService()
        # errors, name_errors, fileId = paycor_service.import_timecards(selected_week_ending, session['odoo_service'], import_type)

        # Use Paycor Excel import instead
        excel_service = ExcelFileService()        
        excel_service.name_errors = []
        excel_service.error_logs = []
        
        uploaded_files = request.files.getlist('uploaded_files[]')
        errors, name_errors, file_ids = excel_service.parse_fixed_files(uploaded_files, selected_week_ending, session['odoo_service'])

        # If errors need to be resolved, return them to UI
        if errors or name_errors:
            redirect_url = url_for('resolve_internal_errors')
            return jsonify({'redirect_url': redirect_url, "errors": errors, "name_errors": name_errors, "fileIds": file_ids})
        # Else, move on to customer timesheets
        elif import_type == "internal":
            return jsonify({'redirect_url': f'/export_fixed_timesheets?week_ending={selected_week_ending}'})
        else:
            redirect_url = url_for('upload_customer_timesheets')
            return jsonify({'redirect_url': redirect_url})
    
    return render_template('timesheet_wizard/upload_internal_timesheets.html')

# Step 2.5 - Resolve Internal Timesheet Conflicts
@airtanker_app.route('/resolve_internal_errors', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def resolve_internal_errors():
    if request.method in ('POST'):
        # airtanker_app.logger.debug("Submitted CSRF token: %s", request.form.get('csrf_token'))
        # airtanker_app.logger.debug("Session CSRF token: %s", session.get('_csrf_token'))
        # update the records in the database
        data_received = request.json
        if data_received:
            push_updates(data_received, session['username'])

        if "import_type" in session and session["import_type"] and session["import_type"] == 'internal':
            selected_week_ending = session['selected_week_ending']
            redirect_url = f'/export_fixed_timesheets?week_ending={selected_week_ending}'
        else:
            redirect_url = url_for('upload_customer_timesheets')
        return jsonify({'redirect_url': redirect_url})           

    return render_template('timesheet_wizard/resolve_internal_errors.html')

# Step 3 - Upload Customer Timesheets
@airtanker_app.route('/upload_customer_timesheets', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def upload_customer_timesheets():
    if request.method in ('POST') :
        # airtanker_app.logger.debug("Submitted CSRF token: %s", request.form.get('csrf_token'))
        # airtanker_app.logger.debug("Session CSRF token: %s", session.get('_csrf_token'))            
        excel_file_service = ExcelFileService()

        excel_file_service.name_errors = []
        excel_file_service.error_logs = []

        uploaded_files = request.files.getlist('uploaded_files[]')        
        errors, name_errors = excel_file_service.parse_external_files(uploaded_files, session['selected_week_ending'])
        if errors or name_errors:
            redirect_url = url_for('resolve_customer_errors')
            return jsonify({'redirect_url': redirect_url, "errors":errors, 'name_errors':name_errors})
        else:
            completion_html = render_template('timesheet_wizard/wizard_complete.html')

            # Return the HTML content in the JSON response
            return jsonify({'success': True, 'completion_html': completion_html})

    return render_template('timesheet_wizard/upload_customer_timesheets.html')

# Step 3 (alternative) - Upload Customer Timesheets with beta ai feature
@airtanker_app.route('/upload_customer_timesheets_ai', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def upload_customer_timesheets_ai():
    if request.method == 'POST':
        # Grab all files and template IDs
        files = request.files.getlist('uploaded_files[]')
        templates = request.form.getlist('file_templates[]')

        print(f"Selected week ending: {session['selected_week_ending']}")
        # Debug‐print how many items and each pair
        print(f"Received {len(files)} files on /upload_customer_timesheets_ai")
        for idx, (f, tmpl) in enumerate(zip(files, templates)):
            print(f"  [{idx}] filename = {f.filename!r}, selected_template = {tmpl!r}")

        return jsonify({
            'redirect_url': url_for(
                'upload_customer_timesheets_ai',
                week_ending_date=session['selected_week_ending']
            )
        })

    try:
        database_service = DatabaseService()
        database_service.connect()
        templates = database_service.get_templates()

    except Exception as e:
        print(f"Failed to get templates: {e}")
        templates = []

    selected_week_ending = session.get('selected_week_ending')
    return render_template('timesheet_wizard/upload_customer_timesheets_ai.html', templates=templates, selected_week_ending=selected_week_ending)


# Step 3.5 - Resolve Customer Timesheet Conflicts
@airtanker_app.route('/resolve_customer_errors', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def resolve_customer_errors():
    if request.method in ('POST'):
        data_received = request.json
        push_updates_customer(data_received, session['username'])
        completion_html = render_template('timesheet_wizard/wizard_complete.html')

        # Return the HTML content in the JSON response
        return jsonify({'success': True, 'completion_html': completion_html})
    return render_template('timesheet_wizard/resolve_customer_errors.html')

#### STEP 4 - APPROVALS
@airtanker_app.route('/approvals/timesheets', methods=['GET', 'POST'])
#@requires_odoo_authentication
@requires_authentication
def approvals():
    form = WorkOrderForm()

    selected_week_ending = session.get('selected_week_ending')
    if not selected_week_ending:
        selected_week_ending = False

    return render_template('endpoints/timesheet_approvals.html', form=form, selected_week_ending=selected_week_ending)

@airtanker_app.route('/update_travel_entries', methods=['POST'])
@requires_odoo_authentication
@requires_authentication
def update_travel_entries():    
    if request.method in ('POST'):
        data_received = request.json
        if data_received:
            update_travel_hours_for_not_errored_employees(data_received, session['username'])
        return jsonify(status='success'), 200

@airtanker_app.route('/import_fixed_time', methods=['GET', 'POST'])
@requires_odoo_authentication
@requires_authentication
def import_fixed_time():
    return render_template('timesheet_wizard/upload_fixed_timesheets.html')