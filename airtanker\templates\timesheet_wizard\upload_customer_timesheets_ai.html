<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{{ url_for('static', filename='assets/favicon-32x32.png') }}" type="image/png">
    <title>AirTanker</title>

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.2/css/all.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles_customer.css') }}"  />

    <style>
        .file-upload-wrapper {
            border: 2px dashed #91b0b3;
            border-radius: 10px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            margin: 0 10px;
            transition: background-color 0.2s;
        }
        .file-upload-wrapper:hover {
            background-color: #f3f4f6;
        }
        .file-upload-wrapper i {
            color: #0f57f3;
        }

        .progress-container {
            width: 80%;
            background: #e0e0e0;
            position: fixed;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
        }
        .progress-bar {
            height: 7px;
            background: #4caf50;
            animation: loadProgress 2s forwards;
        }
        @keyframes loadProgress {
            from { width: 42%; }
            to   { width: 87%; }
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .loading-image {
            width: 220px;
            height: 220px;
            background: url('/static/assets/loadingOdoo-unscreen-crop.gif') no-repeat center;
            background-size: contain;
        }

        .loading-text {
            font-size: 1.1rem;
            color: #6c757d;
            font-weight: 500;
            margin: 0;
        }

        .modal-content {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        }
    </style>
</head>

<body class="bg-gradient-white">
    <!-- Modal for the loading overlay -->
    <div class="modal" id="spinnerModal" tabindex="-1" aria-hidden="true" data-backdrop="static" data-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content text-center p-4">
                <div class="loading-container">
                    <div class="loading-image"></div>
                    <p class="loading-text">Processing your files...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="progress-container">
        <div class="progress-bar" id="progressBar"></div>
    </div>
    <div id="progressLabel" class="text-center" style="position: fixed; top: 20px; width: 100%; z-index: 1000;">
        Step 3: Upload Customer Provided TimeSheets
    </div>

    <!-- The file upload areas -->
    <div class="wrapper fadeIn" id="content">
        <div id="formContent">
            <div class="d-flex justify-content-center align-items-center mb-4" style="margin-top:5%; position: relative;">
                <h1 class="h4 text-gray-900 mb-0">
                    Upload Customer Timesheets
                </h1>
                {% if selected_week_ending %}
                <span class="text-muted" style="position: absolute; right: 20; font-size: 0.9rem;">
                    WE: {{ selected_week_ending }}
                </span>
                {% endif %}
            </div>
            <div class="file-upload-wrapper" onclick="document.getElementById('file-upload').click()">
                <i class="fas fa-cloud-upload-alt fa-2x"></i>
                <p>Drag files here or <a href="#" onclick="handleBrowseClick(event)">Browse</a></p>
            </div>
            <input id="file-upload" type="file" name="uploaded_files[]" multiple accept=".xlsx, .xlsm, .pdf" style="display: none;" onchange="addFiles()">
            <ul class="list-group mt-3 mb-4" id="file-list">
                <!-- Files will be listed here -->
            </ul>
            <div id="formFooter" class="mt-3" style="display: flex; justify-content: space-between; align-items: center;">
                <!-- <a href="/" class="underlineHover">Cancel</a> -->
                <input type="button" class="btn btn-danger" value="Cancel" onclick="window.location.href='/'" style="color:#fff; background-color: #dc3545; margin: 0; box-shadow: 0 10px 30px 0 rgb(233 95 95 / 40%);">
                <input id="process-files-btn" type="button" class="btn btn-primary" value="Process Files" onclick="processFiles();" disabled=true style="margin: 0;">
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>

    <script>
        const templatesMeta = [
            {% for t in templates %}
                {
                id: "{{ t.id }}",
                identifiers: {{ t.identifiers|tojson }}
                }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        let selectedFiles = [];
        let selectedTemplates = [];

        function handleBrowseClick(event) {
            event.stopPropagation();
            document.getElementById('file-upload').click();
        }

        function addFiles() {
            const files = document.getElementById('file-upload').files;
            for (let f of files) {
                selectedFiles.push(f);
                const matched = autoSelectTemplate(f.name); // Try to automatch first
                selectedTemplates.push(matched);
            }
            updateFileList();
            document.getElementById('file-upload').value = '';
        }

        function autoSelectTemplate(filename) {
            const lower = filename.toLowerCase();
            for (let meta of templatesMeta) {
                if (Array.isArray(meta.identifiers)) {
                for (let idf of meta.identifiers) {
                        if (idf && lower.includes(String(idf).toLowerCase())) {
                            return String(meta.id);
                        }
                    }
                }
            }
            return null;
        }

        function updateFileList() {
            const ul = document.getElementById('file-list');
            ul.innerHTML = '';
            selectedFiles.forEach((file, i) => {
                ul.innerHTML += `
                    <li class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <i class="fas fa-file-alt"></i>
                                ${file.name}
                            </div>
                            <div class="d-flex align-items-center">
                                <div class="form-group mb-0 mx-2">
                                    <select class="form-control form-control-sm" id="template-${i}" onchange="updateFileTemplate(${i}, this.value)">
                                        {% for template in templates %}
                                        <option value="{{ template.id }}" {% if template.is_default %}selected{% endif %}>
                                            {{ template.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <i class="fas fa-times text-danger" style="cursor:pointer;" onclick="removeFile(${i})"></i>
                            </div>
                        </div>
                    </li>
                `;
            });

            // After building HTML, restore any previously selected templates to the selects
            for (let i = 0; i < selectedFiles.length; i++) {
                const select = document.getElementById(`template-${i}`);
                if (selectedTemplates[i] !== null) {
                    select.value = selectedTemplates[i];
                }
            }

            // For any new files (null), set selectedTemplates to the default value from the select
            for (let i = 0; i < selectedFiles.length; i++) {
                const select = document.getElementById(`template-${i}`);
                if (selectedTemplates[i] === null) {
                    selectedTemplates[i] = select.value;
                }
            }

            document.getElementById('process-files-btn').disabled = selectedFiles.length > 0 ? false : true;
        }

        function updateFileTemplate(fileIndex, templateId) {
            selectedTemplates[fileIndex] = templateId;
        }

        function removeFile(index) {
            selectedFiles.splice(index, 1);
            selectedTemplates.splice(index, 1);
            updateFileList();
        }

        function processFiles() {
            $('#spinnerModal').modal('show');
            const fd = new FormData();
            selectedFiles.forEach((f, i) => {
                fd.append('uploaded_files[]', f);
                fd.append('file_templates[]', selectedTemplates[i]);
            });

            fetch('/upload_customer_timesheets_ai', {
                method: 'POST',
                body: fd,
                credentials: 'same-origin'
            })
                .then(res => {
                    if (!res.ok) throw new Error(res.statusText);
                    return res.json();
                })
                .then(data => {
                    if (data.redirect_url) {
                        sessionStorage.setItem('errors', JSON.stringify(data.errors));
                        sessionStorage.setItem(
                            'name_errors',
                            JSON.stringify(data.name_errors)
                        );
                        window.location = data.redirect_url;
                    } else if (data.completion_html) {
                        document.body.innerHTML = data.completion_html;
                        setTimeout(
                            () => (window.location = '/approvals/timesheets'),
                            2500
                        );
                    }
                })
                .catch(err => {
                    console.error(err);
                    alert('An error occurred. Please try again.');
                    $('#spinnerModal').modal('hide');
                });
        }

        document.addEventListener("DOMContentLoaded", () => {
            setTimeout(
                () => (document.getElementById('progressLabel').style.display = 'block'),
                2000
            );
        });
    </script>
</body>