# Sunday Calendar Component Migration Guide

This guide shows how to replace your existing date picker implementations with the new reusable SundayCalendar component.

## Quick Start

### 1. Include Required Files

Add these lines to your template's `<head>` section:

```html
<link rel="stylesheet" href="{{ url_for('static', filename='css/sunday-calendar.css') }}">
<script src="{{ url_for('static', filename='js/sunday-calendar.js') }}"></script>
<script src="{{ url_for('static', filename='js/sunday-calendar-integration.js') }}"></script>
```

### 2. Auto-Initialize (Easiest Method)

Replace your existing JavaScript initialization with:

```javascript
document.addEventListener('DOMContentLoaded', autoInitializeCalendars);
```

This will automatically detect and replace:
- `#sundayPicker` inputs (timesheet wizard pages)
- `#weekEndingPicker` inputs (dashboard/approval pages)

## Manual Migration Examples

### Timesheet Wizard Pages

**Before:**
```html
<div>
    {{ form.sundayPicker.label }}<br>
    {{ form.sundayPicker(size=20, class_="date-input", onchange="checkSunday(this)", style="margin-bottom:20px; text-align: center") }}
</div>

<script>
function setMostRecentSunday() {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const difference = dayOfWeek % 7;
    const mostRecentSunday = new Date(today.setDate(today.getDate() - difference));
    document.getElementById('sundayPicker').valueAsDate = mostRecentSunday;
}

function checkSunday(input) {
    const selectedDate = new Date(input.value);
    if (selectedDate.getDay() !== 6) {
        alert('Please select a Sunday.');
        setMostRecentSunday();
    }
}
</script>
```

**After:**
```html
<div>
    {{ form.sundayPicker.label }}<br>
    {{ form.sundayPicker(size=20, class_="date-input", style="display:none;") }}
    <div id="sunday-calendar-container"></div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initTimesheetWizardCalendar();
});
</script>
```

### Dashboard/Approval Pages

**Before:**
```html
<div class="date-picker-container text-center mb-3">
    <label for="weekEndingPicker">Select a Week Ending:</label>
    <br>
    <input type="date" id="weekEndingPicker" class="date-picker" onchange="setDateField(this.value)">
</div>

<script>
function setDateField(value) {
    const date = new Date(value);
    if (date.getDay() === 6) {
        document.getElementById('weekEndingPicker').value = date.toISOString().split('T')[0];
    } else {
        alert('Please select a Sunday.');
        setMostRecentSunday();
    }
}
</script>
```

**After:**
```html
<div class="date-picker-container text-center mb-3">
    <label for="weekEndingPicker">Select a Week Ending:</label>
    <br>
    <input type="date" id="weekEndingPicker" class="date-picker" style="display:none;">
    <div id="week-ending-calendar-container"></div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initDashboardCalendar();
});
</script>
```

## Advanced Integration Options

### Custom Callbacks

```javascript
initDashboardCalendar({
    onDateSelect: (selectedSunday) => {
        console.log('Selected:', selectedSunday);
        // Custom logic here
        fetchData(); // Call your existing functions
    }
});
```

### Modal Integration

```javascript
const modalCalendar = createModalCalendar('myModal', 'myInput', {
    onDateSelect: (sunday) => {
        // Handle selection
        $('#myModal').modal('hide');
    }
});
```

### Compact Version for Dashboards

```javascript
const compactCalendar = createCompactCalendar('dashboard-calendar', {
    onDateSelect: (sunday) => {
        updateDashboard(sunday);
    }
});
```

## Benefits of Migration

1. **Consistent UI**: All date pickers will have the same look and feel
2. **Better UX**: Visual week highlighting and Sunday emphasis
3. **Mobile Friendly**: Responsive design that works on all devices
4. **Reusable**: One component for all Sunday selection needs
5. **Maintainable**: Centralized logic instead of scattered code

## Backward Compatibility

The integration utilities maintain backward compatibility with your existing:
- Form field names (`sundayPicker`, `weekEndingPicker`)
- Change event handlers
- Validation logic
- Data fetching functions (`fetchData`, `fetchWeeklyData`, etc.)

## File Structure

```
airtanker/static/
├── css/
│   └── sunday-calendar.css          # Calendar styles
├── js/
│   ├── sunday-calendar.js           # Core calendar component
│   └── sunday-calendar-integration.js  # Integration utilities
└── docs/
    └── sunday-calendar-migration.md # This guide
```

## Testing

After migration, test that:
1. Date selection still updates the hidden input field
2. Form submissions work correctly
3. Existing JavaScript functions are still called
4. The calendar initializes with the correct default date
5. Mobile responsiveness works properly

## Troubleshooting

### Calendar doesn't appear
- Check that CSS and JS files are loaded
- Verify container element exists
- Check browser console for errors

### Form submission issues
- Ensure hidden input field is properly updated
- Verify form field names match your backend expectations

### Styling conflicts
- The calendar uses its own CSS classes
- Bootstrap compatibility is built-in
- Add custom styles to override if needed

## Support

For issues or questions about the Sunday Calendar component:
1. Check the example template: `templates/examples/sunday-calendar-example.html`
2. Review the integration utilities in `sunday-calendar-integration.js`
3. Test with the provided examples first
